import React, { useState, createContext, useContext, useEffect } from 'react';
import {
  FieldErrorProps,
  FieldTemplateProps,
  ObjectFieldTemplateProps,
  ArrayFieldTemplateItemType,
  ArrayFieldTemplateProps,
} from '@rjsf/utils';
import HelpIcon from '@mui/icons-material/Help';
import { IconButton, Tooltip, Box, Button } from '@mui/material';
import { ExpandLess, ExpandMore, NorthOutlined, SouthOutlined, DeleteOutlineOutlined, Add } from '@mui/icons-material';
import './InfoFieldTemplate.css';


const ArrayItemContext = createContext<{
  removeButton?: React.ReactNode;
  moveUpButton?: React.ReactNode;
  moveDownButton?: React.ReactNode;
}>({});

const CardStyleContext = createContext<{
  shouldUseCardStyle?: boolean;
  setShouldUseCardStyle?: (value: boolean) => void;
  labelInfo?: {
    label?: string;
    required?: boolean;
    description?: string;
    rawDescription?: string;
    id?: string;
  };
}>({});

const customObjectStyle = {
  backgroundColor: '#F0F0F0',
  padding: '24px',
  borderRadius: '12px',
  border: '1px solid #D0D0D0',
  marginBottom: '8px'
};

const customLabelStyles = {
  fontSize: '20px',
  fontWeight: 500,  
  fontFamily: 'Roboto',
};

const customLabelTextStyles = {
  fontWeight: 400,  
  fontSize: '16px',
  fontFamily: 'Roboto',
  color: '#222229'
};

const customListItemStyles = {
  whiteSpace: 'nowrap',
};

const buttonContainerStyles = {
  display: 'flex',
  alignItems: 'center',
  gap: '4px'
};

const floatLeftStyle = {
  float: 'left' as const,
  display: 'flex',
  alignItems: 'center'
};

const floatRightStyle = {
  float: 'right' as const,
  ...buttonContainerStyles
};

const LabelComponent: React.FC<{
  label: string;
  required?: boolean;
  description?: string;
  rawDescription?: string;
  id?: string;
  style?: React.CSSProperties;
}> = ({ label, required, description, rawDescription, id, style }) => (
  <label style={style} htmlFor={id}>
    {label}
    {required ? <span style={{ color: 'red' }}>*</span> : null}
    {description !== '' ? (
      <Tooltip title={rawDescription}>
        <IconButton aria-label='info' size='small'>
          <HelpIcon fontSize='small' />
        </IconButton>
      </Tooltip>
    ) : null}
  </label>
);

const ExpandCollapseButton: React.FC<{
  isExpanded: boolean;
  onToggle: () => void;
}> = ({ isExpanded, onToggle }) => (
  <>
    {!isExpanded && (
      <ExpandMore
        fontSize='medium'
        color='action'
        onClick={onToggle}
      />
    )}
    {isExpanded && (
      <ExpandLess
        fontSize='medium'
        color='action'
        onClick={onToggle}
      />
    )}
  </>
);

const MoveButtons: React.FC<{
  moveUpButton?: React.ReactNode;
  moveDownButton?: React.ReactNode;
}> = ({ moveUpButton, moveDownButton }) => (
  <>
    {moveUpButton}
    {moveDownButton}
  </>
);

const CustomFieldTemplate: React.FC<FieldTemplateProps> = ({
  id,
  classNames,
  label,
  required,
  description,
  errors,
  children,
  schema,
  rawDescription,
}) => {
  const descriptionForNestedProperties = description.props.description;
  const [shouldUseCardStyle, setShouldUseCardStyle] = useState(false);


  const isObjectOrArray = schema.type === 'array' || schema.type === 'object';

  const currentLabelInfo = {
    label,
    required,
    description: descriptionForNestedProperties,
    rawDescription,
    id,
  };

  return (
    <CardStyleContext.Provider value={{
      shouldUseCardStyle,
      setShouldUseCardStyle,
      labelInfo: currentLabelInfo
    }}>
      <div
        style={isObjectOrArray && !shouldUseCardStyle ? customObjectStyle : null}
        className={`${classNames} ${shouldUseCardStyle ? 'card-shadow-white' : ''}`}
      >
        {!shouldUseCardStyle && (
          <LabelComponent
            label={label}
            required={required}
            description={descriptionForNestedProperties}
            rawDescription={rawDescription}
            id={id}
            style={isObjectOrArray ? customLabelStyles : customLabelTextStyles}
          />
        )}

        <span
          className={schema?.readOnly ? 'disabled' : ''}
          title={schema.readOnly ? 'Read-only field' : ''}
        >
          {children}
        </span>
        {errors}
      </div>
    </CardStyleContext.Provider>
  );
};

const CustomObjectFieldTemplate: React.FC<ObjectFieldTemplateProps> = ({
  idSchema,
  properties,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);
  const { removeButton, moveUpButton, moveDownButton } = useContext(ArrayItemContext);
  const { setShouldUseCardStyle, labelInfo } = useContext(CardStyleContext);

  if (!properties.length) return null;

  const hasDirectFields = properties.some(prop => {
    const propSchema = prop.content?.props?.schema;
    return propSchema?.type && ['string', 'number', 'boolean', 'integer'].includes(propSchema.type);
  });

  const hasCheckboxFields = properties.some(prop => {
    const propSchema = prop.content?.props?.schema;
    // Check if this property will render with a checkbox (FieldHOC applies to string and boolean fields)
    return propSchema?.type && ['string', 'boolean'].includes(propSchema.type);
  });

  const hasArrayItemButtons = moveUpButton || moveDownButton || removeButton;

  useEffect(() => {
    if (setShouldUseCardStyle && hasDirectFields && hasArrayItemButtons) {
      setShouldUseCardStyle(true);
    }
  }, [setShouldUseCardStyle, hasDirectFields, hasArrayItemButtons]);

  const containerClass = hasDirectFields && !hasArrayItemButtons ? 'card-shadow-white' : 'card-shadow';

  return (
    <>
      <span id={idSchema.$id} />

      {hasArrayItemButtons && hasDirectFields && labelInfo && (
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          marginBottom: isExpanded ? '16px' : '0',
          minHeight: '32px'
        }}>
          {/* Left side: Move buttons + Label */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <MoveButtons moveUpButton={moveUpButton} moveDownButton={moveDownButton} />
            <LabelComponent
              label={labelInfo.label}
              required={labelInfo.required}
              description={labelInfo.description}
              rawDescription={labelInfo.rawDescription}
              id={labelInfo.id}
              style={customLabelStyles}
            />
          </div>

          {/* Right side: Remove button and expand/collapse arrow */}
          <div style={buttonContainerStyles}>
            {removeButton}
            <ExpandCollapseButton
              isExpanded={isExpanded}
              onToggle={() => setIsExpanded(prev => !prev)}
            />
          </div>
        </div>
      )}

      {hasArrayItemButtons && !hasDirectFields && (
        <>
          {/* Move buttons positioned on the left side */}
          {(moveUpButton || moveDownButton) && (
            <span style={floatLeftStyle}>
              <MoveButtons moveUpButton={moveUpButton} moveDownButton={moveDownButton} />
            </span>
          )}

          {/* Remove button and expand/collapse arrow positioned on the right side */}
          <span style={floatRightStyle}>
            {removeButton}
            <ExpandCollapseButton
              isExpanded={isExpanded}
              onToggle={() => setIsExpanded(prev => !prev)}
            />
          </span>
        </>
      )}

      {/* Expand/collapse arrow for objects without array item buttons */}
      {!hasArrayItemButtons && (
        <span style={floatRightStyle}>
          <ExpandCollapseButton
            isExpanded={isExpanded}
            onToggle={() => setIsExpanded(prev => !prev)}
          />
        </span>
      )}

      {isExpanded && (
        <div className={hasDirectFields && hasArrayItemButtons ? 'layout-container' : containerClass}>
          {hasCheckboxFields && hasDirectFields && (
            <div style={{
              display: 'flex',
              marginBottom: '8px'
            }}>
              <div style={{ flex: 1 }}></div>
              <div style={{
                width: '100px',
                paddingLeft: '8px',
                textAlign: 'center',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{
                  ...customLabelTextStyles,
                  whiteSpace: 'nowrap',
                  fontSize: '14px'
                }}>
                  Allow Override?
                </span>
              </div>
            </div>
          )}
          {properties.map(items => (
            <div key={items.name} className='property-wrapper field-content'>
              {items.content}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

const CustomErrorFieldTemplate: React.FC<FieldErrorProps> = ({ errors }) => {
  if (!errors?.length) return null;

  return (
    <div>
      <ul className='error-detail bs-callout bs-callout-info'>
        {errors.map(error => (
          <li
            key={error.toString()}
            className='text-danger'
            style={customListItemStyles}
          >
            {error}
          </li>
        ))}
      </ul>
    </div>
  );
};

const CustomArrayFieldItemTemplate: React.FC<ArrayFieldTemplateItemType> = ({
  children,
  disabled,
  hasRemove,
  hasMoveDown,
  hasMoveUp,
  index,
  onDropIndexClick,
  onReorderClick,
  readonly,
  className,
}) => {
  const itemStyle = {
    display: 'flex',
    alignItems: 'flex-start',
    gap: '8px',
    padding: '8px',
    marginBottom: '8px',
    position: 'relative' as const,
  };

  const buttonStyle = {
    minWidth: 'auto',
    padding: '4px',
  };


  const removeButton = hasRemove ? (
    <IconButton
      style={buttonStyle}
      onClick={onDropIndexClick(index)}
      disabled={disabled || readonly}
      size="small"
      title="Remove"
    >
      <DeleteOutlineOutlined fontSize="small" sx={{ color: '#48464A' }} />
    </IconButton>
  ) : null;

  const moveUpButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveUp ? onReorderClick(index, index - 1) : undefined}
      disabled={disabled || readonly || !hasMoveUp}
      size="small"
      title="Move Up"
    >
      <NorthOutlined fontSize="small" sx={{ color: (disabled || readonly || !hasMoveUp) ? '#48464A' :  '#27725B' }} />
    </IconButton>
  );

  const moveDownButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveDown ? onReorderClick(index, index + 1) : undefined}
      disabled={disabled || readonly || !hasMoveDown}
      size="small"
      title="Move Down"
    >
      <SouthOutlined fontSize="small" sx={{ color: (disabled || readonly || !hasMoveDown) ? '#48464A' : '#27725B' }}/>
    </IconButton>
  );

  return (
    <ArrayItemContext.Provider value={{ removeButton, moveUpButton, moveDownButton }}>
      <Box style={itemStyle} className={className}>
        <Box style={{ flex: 1 }}>
          {children}
        </Box>
      </Box>
    </ArrayItemContext.Provider>
  );
};

const CustomArrayFieldTemplate: React.FC<ArrayFieldTemplateProps> = ({
  canAdd,
  disabled,
  items,
  onAddClick,
  readonly,
}) => {
  const addButtonStyle = {
    marginTop: '16px',
    textTransform: 'none' as const,
    fontSize: '14px',
    padding: '8px 16px',
    borderRadius: '8px',
  };

  // console.log('ArrayFieldTemplate items:', items);

  return (
    <Box>
      {items && items.map((itemProps) => (
        <CustomArrayFieldItemTemplate
          key={itemProps.key}
          {...itemProps}
        />
      ))}

      {/* Add button */}
      {canAdd && (
        <Button
          style={addButtonStyle}
          onClick={onAddClick}
          disabled={disabled || readonly}
          endIcon={<Add />}
          variant='contained'
        >
          Add Card
        </Button>
      )}
    </Box>
  );
};

export {
  CustomFieldTemplate,
  CustomObjectFieldTemplate,
  CustomErrorFieldTemplate,
  CustomArrayFieldItemTemplate,
  CustomArrayFieldTemplate,
};
